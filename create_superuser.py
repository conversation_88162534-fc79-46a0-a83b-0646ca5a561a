#!/usr/bin/env python
import os
import sys
import django

# Add the project directory to the Python path
sys.path.insert(0, '/src')

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'hive.settings')
django.setup()

from django.contrib.auth import get_user_model
from contacts.models import Contact

User = get_user_model()

# Check if user already exists
if User.objects.filter(email='<EMAIL>').exists():
    print("User <NAME_EMAIL> already exists!")
    user = User.objects.get(email='<EMAIL>')
    print(f"Existing user: {user.username} ({user.email})")
else:
    # Create a contact first
    contact = Contact.objects.create(
        first_name='<PERSON>',
        last_name='<PERSON><PERSON><PERSON>',
        email='<EMAIL>'
    )

    # Create superuser using the custom manager
    user = User.objects.create_user(
        username='sean',
        password='admin123',
        email='<EMAIL>',
        is_superuser=True,
        is_staff=True,
        contact=contact
    )
    print(f"Superuser created successfully!")
    print(f"Username: {user.username}")
    print(f"Email: {user.email}")
    print(f"Password: admin123")

print("\nYou can now log in to:")
print("- Django Admin: https://my.boldidea.local:8000/admin/")
print("- Main Portal: https://my.boldidea.local:8000/")
